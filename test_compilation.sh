#!/bin/bash

echo "Testing LaTeX compilation..."

# Check if pdflatex is available
if ! command -v pdflatex &> /dev/null; then
    echo "❌ pdflatex not found. Please install LaTeX:"
    echo ""
    echo "For macOS:"
    echo "  brew install --cask mactex"
    echo ""
    echo "For Ubuntu/Debian:"
    echo "  sudo apt-get install texlive-full"
    echo ""
    echo "For Windows:"
    echo "  Download MiKTeX from https://miktex.org/"
    echo ""
    echo "Alternative: Use Overleaf online at https://www.overleaf.com"
    exit 1
fi

echo "✅ pdflatex found"

# Test compilation of minimal version
echo "Testing minimal version compilation..."
if pdflatex -interaction=nonstopmode solar_energy_minimal.tex > /dev/null 2>&1; then
    echo "✅ solar_energy_minimal.tex compiled successfully"
    if [ -f "solar_energy_minimal.pdf" ]; then
        echo "✅ PDF generated: solar_energy_minimal.pdf"
    fi
else
    echo "❌ solar_energy_minimal.tex failed to compile"
    echo "Check the log file for errors:"
    echo "  cat solar_energy_minimal.log"
fi

# Clean up auxiliary files
rm -f *.aux *.log *.out *.toc

echo ""
echo "Compilation test complete!"
echo ""
echo "If compilation failed, try using Overleaf:"
echo "1. Go to https://www.overleaf.com"
echo "2. Create a free account"
echo "3. Upload solar_energy_minimal.tex"
echo "4. Click 'Recompile'"
