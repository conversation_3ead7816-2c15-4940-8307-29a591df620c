#!/bin/bash

# LaTeX Compilation Script for Solar Energy Transition Review
# This script compiles the main document and supplementary figures

echo "Compiling Solar Energy Transition Review LaTeX Documents..."

# Compile main document
echo "Compiling main document..."
pdflatex solar_energy_transition_review.tex
bibtex solar_energy_transition_review
pdflatex solar_energy_transition_review.tex
pdflatex solar_energy_transition_review.tex

# Compile supplementary figures
echo "Compiling supplementary figures..."
pdflatex figures_and_tables.tex
pdflatex figures_and_tables.tex

# Clean up auxiliary files
echo "Cleaning up auxiliary files..."
rm -f *.aux *.bbl *.blg *.log *.out *.toc *.lof *.lot

echo "Compilation complete!"
echo "Main document: solar_energy_transition_review.pdf"
echo "Supplementary figures: figures_and_tables.pdf"

# Check if PDFs were created successfully
if [ -f "solar_energy_transition_review.pdf" ]; then
    echo "✓ Main document compiled successfully"
else
    echo "✗ Main document compilation failed"
fi

if [ -f "figures_and_tables.pdf" ]; then
    echo "✓ Supplementary figures compiled successfully"
else
    echo "✗ Supplementary figures compilation failed"
fi
